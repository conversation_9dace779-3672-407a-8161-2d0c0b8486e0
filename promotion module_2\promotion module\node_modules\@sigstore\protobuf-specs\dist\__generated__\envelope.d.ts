/** An authenticated message of arbitrary type. */
export interface Envelope {
    /**
     * Message to be signed. (In JSON, this is encoded as base64.)
     * REQUIRED.
     */
    payload: Buffer;
    /**
     * String unambiguously identifying how to interpret payload.
     * REQUIRED.
     */
    payloadType: string;
    /**
     * Signature over:
     *     PAE(type, payload)
     * Where PAE is defined as:
     * PAE(type, payload) = "DSSEv1" + SP + LEN(type) + SP + type + SP + LEN(payload) + SP + payload
     * +               = concatenation
     * SP              = ASCII space [0x20]
     * "DSSEv1"        = ASCII [0x44, 0x53, 0x53, 0x45, 0x76, 0x31]
     * LEN(s)          = ASCII decimal encoding of the byte length of s, with no leading zeros
     * REQUIRED (length >= 1).
     */
    signatures: Signature[];
}
export interface Signature {
    /**
     * Signature itself. (In JSON, this is encoded as base64.)
     * REQUIRED.
     */
    sig: Buffer;
    /**
     * Unauthenticated* hint identifying which public key was used.
     * OPTIONAL.
     */
    keyid: string;
}
export declare const Envelope: MessageFns<Envelope>;
export declare const Signature: MessageFns<Signature>;
interface MessageFns<T> {
    fromJSON(object: any): T;
    toJSON(message: T): unknown;
}
export {};
