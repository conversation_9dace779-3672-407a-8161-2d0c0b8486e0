{"version": 3, "sources": ["../src/ngtsc/transform/jit/src/downlevel_decorators_transform.ts", "../src/ngtsc/transform/jit/src/initializer_api_transforms/transform.ts", "../src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.ts", "../src/ngtsc/transform/jit/src/initializer_api_transforms/input_function.ts", "../src/ngtsc/transform/jit/src/initializer_api_transforms/model_function.ts", "../src/ngtsc/transform/jit/src/initializer_api_transforms/output_function.ts", "../src/ngtsc/transform/jit/src/initializer_api_transforms/query_functions.ts", "../src/ngtsc/transform/jit/src/index.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAQA,OAAO,QAAQ;AASf,SAASA,oBAAmB,WAAsB,QAAe;AAC/D,SAAO,UAAW,UAAU,WAAW,QAAQ,UAAU,OAAO,SAAS;AAC3E;AAwBA,IAAM,kCAAkC;AASxC,SAAS,mCACP,WACA,aAA4B;AAE5B,QAAM,qBAAoD,CAAA;AAC1D,QAAM,OAAO,UAAU;AACvB,UAAQ,KAAK,MAAM;IACjB,KAAK,GAAG,WAAW;AAEjB,yBAAmB,KAAK,GAAG,QAAQ,yBAAyB,QAAQ,IAAI,CAAC;AACzE;IACF,KAAK,GAAG,WAAW;AAEjB,YAAM,OAAO;AACb,yBAAmB,KAAK,GAAG,QAAQ,yBAAyB,QAAQ,KAAK,UAAU,CAAC;AACpF,UAAI,KAAK,UAAU,QAAQ;AACzB,cAAM,OAAwB,CAAA;AAC9B,mBAAW,OAAO,KAAK,WAAW;AAChC,eAAK,KAAK,GAAG;QACf;AACA,cAAM,mBAAmB,GAAG,QAAQ,6BAClC,GAAG,QAAQ,gBAAgB,MAAM,IAAI,CAAC;AAExC,2BAAmB,KAAK,GAAG,QAAQ,yBAAyB,QAAQ,gBAAgB,CAAC;MACvF;AACA;IACF;AACE,kBAAY,KAAK;QACf,MAAM,UAAU,cAAa;QAC7B,OAAO,UAAU,SAAQ;QACzB,QAAQ,UAAU,OAAM,IAAK,UAAU,SAAQ;QAC/C,aAAa,GACX,GAAG,WAAW,UAAU;QAE1B,UAAU,GAAG,mBAAmB;QAChC,MAAM;OACP;AACD;EACJ;AACA,SAAO,GAAG,QAAQ,8BAA8B,kBAAkB;AACpE;AAeA,SAAS,kCACP,aACA,wBACA,gBACA,0BAAiC;AAEjC,QAAM,SAA0B,CAAA;AAEhC,aAAW,aAAa,gBAAgB;AACtC,QAAI,CAAC,UAAU,QAAQ,UAAU,WAAW,WAAW,GAAG;AACxD,aAAO,KAAK,GAAG,QAAQ,WAAU,CAAE;AACnC;IACF;AAEA,UAAM,YAAY,UAAU,OACxB,0BAA0B,wBAAwB,UAAU,IAAI,IAChE;AACJ,UAAM,UAAU;MACd,GAAG,QAAQ,yBACT,QACA,aAAa,GAAG,QAAQ,iBAAiB,WAAW,CAAC;;AAIzD,UAAM,aAA2C,CAAA;AACjD,eAAW,QAAQ,UAAU,YAAY;AACvC,iBAAW,KAAK,mCAAmC,MAAM,WAAW,CAAC;IACvE;AACA,QAAI,WAAW,QAAQ;AACrB,cAAQ,KACN,GAAG,QAAQ,yBACT,cACA,GAAG,QAAQ,6BAA6B,UAAU,CAAC,CACpD;IAEL;AACA,WAAO,KAAK,GAAG,QAAQ,8BAA8B,OAAO,CAAC;EAC/D;AAEA,QAAM,cAAc,GAAG,QAAQ,oBAC7B,QACA,QACA,CAAA,GACA,QACA,GAAG,QAAQ,YAAY,GAAG,WAAW,sBAAsB,GAC3D,GAAG,QAAQ,6BAA6B,QAAQ,IAAI,CAAC;AAEvD,QAAM,WAAW,GAAG,QAAQ,0BAC1B,CAAC,GAAG,QAAQ,YAAY,GAAG,WAAW,aAAa,CAAC,GACpD,kBACA,QACA,QACA,WAAW;AAEb,MAAI,0BAA0B;AAC5B,OAAG,4BAA4B,UAAU;MACvC;QACE,MAAM,GAAG,WAAW;QACpB,MAAM;UACJ;UACA;UACA;UACA,+BAA+B;UAC/B;UACA;UACA;UACA,KAAK,IAAI;QACX,KAAK;QACL,KAAK;QACL,oBAAoB;;KAEvB;EACH;AACA,SAAO;AACT;AAUA,SAAS,0BACP,wBACA,MAAiB;AAEjB,MAAI,OAAO,KAAK;AAChB,MAAI,GAAG,kBAAkB,IAAI,GAAG;AAE9B,WAAO,KAAK,QAAQ;EACtB;AACA,UAAQ,MAAM;IACZ,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,UAAU;IAC/C,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,OAAO;IAC5C,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,SAAS;IAC9C,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,QAAQ;IAC7C,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,QAAQ;IAC7C,KAAK,GAAG,WAAW;IACnB,KAAK,GAAG,WAAW;AACjB,aAAO,GAAG,QAAQ,iBAAiB,QAAQ;IAC7C,KAAK,GAAG,WAAW;AACjB,YAAM,UAAU;AAEhB,aAAO,uBAAuB,QAAQ,QAAQ;IAChD,KAAK,GAAG,WAAW;AACjB,YAAM,iBAAkB,KAA0B,MAAM,OACtD,CAAC,MAAM,EAAE,GAAG,kBAAkB,CAAC,KAAK,EAAE,QAAQ,SAAS,GAAG,WAAW,YAAY;AAEnF,aAAO,eAAe,WAAW,IAC7B,0BAA0B,wBAAwB,eAAe,EAAE,IACnE;IACN;AACE,aAAO;EACX;AACF;AASA,SAAS,qBAAqB,aAA6B,QAAiB;AAC1E,MAAI,OAAO,QAAQ,GAAG,YAAY,OAAO;AACvC,aAAS,YAAY,iBAAiB,MAAM;EAC9C;AAIA,UAAQ,OAAO,QAAQ,GAAG,YAAY,QAAQ,GAAG,YAAY,uBAAuB;AACtF;AA0BM,SAAU,gCACd,aACA,MACA,aACA,QACA,0BACA,sBAA6D;AAE7D,WAAS,uBAAuB,MAAe,WAAiB;AAC9D,QAAI,CAAC,0BAA0B;AAC7B;IACF;AAEA,OAAG,4BAA4B,MAAM;MACnC;QACE,MAAM,GAAG,WAAW;QACpB,MAAM,YAAY;QAClB,KAAK;QACL,KAAK;QACL,oBAAoB;;KAEvB;EACH;AAYA,WAAS,kCACPC,cACA,YAAuC;AAIvC,UAAM,UAAyC,CAAA;AAC/C,eAAW,CAAC,MAAM,UAAU,KAAK,WAAW,QAAO,GAAI;AACrD,cAAQ,KACN,GAAG,QAAQ,yBACT,MACA,GAAG,QAAQ,6BACT,WAAW,IAAI,CAAC,SAAS,mCAAmC,MAAMA,YAAW,CAAC,CAAC,CAChF,CACF;IAEL;AACA,UAAM,cAAc,GAAG,QAAQ,8BAA8B,SAAS,IAAI;AAC1E,UAAM,OAAO,GAAG,QAAQ,0BACtB,CAAC,GAAG,QAAQ,YAAY,GAAG,WAAW,aAAa,CAAC,GACpD,kBACA,QACA,QACA,WAAW;AAEb,2BAAuB,MAAM,mBAAmB,kCAAkC;AAClF,WAAO;EACT;AAEA,SAAO,CAAC,YAAqC;AAM3C,UAAM,2BAA2B,sCAAsC,OAAO;AAQ9E,aAAS,uBAAuB,MAAmB;AACjD,YAAM,SAAS,YAAY,oBAAoB,IAAI;AAGnD,UACE,CAAC,UACD,CAAC,qBAAqB,aAAa,MAAM,KACzC,CAAC,OAAO,gBACR,OAAO,aAAa,WAAW,GAC/B;AACA,eAAO;MACT;AAGA,UAAI,GAAG,gBAAgB,IAAI,GAAG;AAC5B,cAAM,gBAAgB,uBAAuB,KAAK,IAAI;AACtD,YAAI,kBAAkB,QAAW;AAC/B,iBAAO;QACT;AACA,eAAO,GAAG,QAAQ,+BAA+B,eAAe,KAAK,KAAK;MAC5E;AACA,YAAM,OAAO,OAAO,aAAa;AAIjC,UAAI,yBAAyB,IAAI,GAAG;AAClC,6EAA0B,IAAI;AAW9B,YAAI,KAAK,SAAS,QAAW;AAC3B,iBAAO,GAAG,gBAAgB,GAAG,QAAQ,iBAAiB,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI;QAClF;MACF;AAIA,aAAO,GAAG,gBAAgB,GAAG,QAAQ,iBAAiB,KAAK,IAAI,GAAG,IAAI;IACxE;AAOA,aAAS,sBACP,SAAwB;AAExB,gBAAU,GAAG,eAAe,SAAS,2BAA2B,OAAO;AACvE,YAAM,mBAAmC,CAAA;AACzC,YAAM,UAA0B,CAAA;AAChC,YAAM,aAAa,KAAK,2BAA2B,OAAO,KAAK,CAAA;AAC/D,iBAAW,aAAa,YAAY;AAGlC,cAAM,gBAAgB,UAAU;AAChC,YAAI,CAACD,oBAAmB,WAAW,MAAM,GAAG;AAC1C,2BAAiB,KAAK,aAAa;AACnC;QACF;AACA,gBAAQ,KAAK,aAAa;MAC5B;AACA,UAAI,CAAC,QAAQ;AAAQ,eAAO,CAAC,QAAW,SAAS,CAAA,CAAE;AAEnD,UAAI,CAAC,QAAQ,QAAQ,CAAC,GAAG,aAAa,QAAQ,IAAI,GAAG;AAGnD,oBAAY,KAAK;UACf,MAAM,QAAQ,cAAa;UAC3B,OAAO,QAAQ,SAAQ;UACvB,QAAQ,QAAQ,OAAM,IAAK,QAAQ,SAAQ;UAC3C,aAAa;UACb,UAAU,GAAG,mBAAmB;UAChC,MAAM;SACP;AACD,eAAO,CAAC,QAAW,SAAS,CAAA,CAAE;MAChC;AAEA,YAAM,mBAAmB,GAAG,iBAAiB,OAAO,IAAI,GAAG,aAAa,OAAO,IAAI;AACnF,UAAI;AAEJ,UAAI,iBAAiB,WAAU,qDAAkB,SAAQ;AACvD,oBAAY,GAAG,aACb,GAAG,QAAQ,gBAAgB,CAAC,GAAG,kBAAkB,GAAI,oBAAoB,CAAA,CAAG,CAAC,GAC5E,QAA4B,SAAS;MAE1C;AAEA,aAAO,CAAC,QAAQ,KAAK,MAAM,+BAA+B,SAAS,SAAS,GAAG,OAAO;IACxF;AAMA,aAAS,qBACP,MAA+B;AAE/B,aAAO,GAAG,eAAe,MAAM,2BAA2B,OAAO;AAEjE,YAAM,gBAA2C,CAAA;AACjD,YAAM,gBAAgB,KAAK;AAC3B,YAAM,iBAA4C,CAAA;AAElD,iBAAW,SAAS,eAAe;AACjC,cAAM,mBAAmC,CAAA;AACzC,cAAM,YAAqC,EAAC,YAAY,CAAA,GAAI,MAAM,KAAI;AACtE,cAAM,aAAa,KAAK,2BAA2B,KAAK,KAAK,CAAA;AAE7D,mBAAW,aAAa,YAAY;AAGlC,gBAAM,gBAAgB,UAAU;AAChC,cAAI,CAACA,oBAAmB,WAAW,MAAM,GAAG;AAC1C,6BAAiB,KAAK,aAAa;AACnC;UACF;AACA,oBAAW,WAAW,KAAK,aAAa;QAC1C;AACA,YAAI,MAAM,MAAM;AAKd,oBAAW,OAAO,MAAM;QAC1B;AACA,uBAAe,KAAK,SAAS;AAG7B,YAAI;AACJ,cAAM,iBAAiB,GAAG,aAAa,KAAK;AAE5C,YAAI,iBAAiB,WAAU,iDAAgB,SAAQ;AACrD,sBAAY,CAAC,GAAG,kBAAkB,GAAI,kBAAkB,CAAA,CAAG;QAC7D;AAEA,cAAM,WAAW,GAAG,QAAQ,2BAC1B,OACA,WACA,MAAM,gBACN,MAAM,MACN,MAAM,eACN,MAAM,MACN,MAAM,WAAW;AAEnB,sBAAc,KAAK,QAAQ;MAC7B;AACA,YAAM,UAAU,GAAG,QAAQ,6BACzB,MACA,GAAG,aAAa,IAAI,GACpB,eACA,KAAK,IAAI;AAEX,aAAO,CAAC,SAAS,cAAc;IACjC;AASA,aAAS,0BAA0B,WAA8B;AAC/D,YAAM,aAAgC,CAAA;AACtC,YAAM,sBAAsB,oBAAI,IAAG;AACnC,UAAI,kBAAoD;AAExD,iBAAW,UAAU,UAAU,SAAS;AACtC,gBAAQ,OAAO,MAAM;UACnB,KAAK,GAAG,WAAW;UACnB,KAAK,GAAG,WAAW;UACnB,KAAK,GAAG,WAAW;UACnB,KAAK,GAAG,WAAW,mBAAmB;AACpC,kBAAM,CAAC,MAAM,WAAW,UAAU,IAAI,sBAAsB,MAAM;AAClE,uBAAW,KAAK,SAAS;AACzB,gBAAI;AAAM,kCAAoB,IAAI,MAAM,UAAU;AAClD;UACF;UACA,KAAK,GAAG,WAAW,aAAa;AAC9B,kBAAM,OAAO;AACb,gBAAI,CAAC,KAAK;AAAM;AAChB,kBAAM,CAAC,WAAW,cAAc,IAAI,qBAClC,MAAmC;AAErC,8BAAkB;AAClB,uBAAW,KAAK,SAAS;AACzB;UACF;UACA;AACE;QACJ;AACA,mBAAW,KAAK,GAAG,eAAe,QAAQ,2BAA2B,OAAO,CAAC;MAC/E;AAIA,YAAM,4BAA4B,KAAK,2BAA2B,SAAS,KAAK,CAAA;AAIhF,YAAM,sBAAsB,0BAA0B,KAAK,CAAC,MAC1DA,oBAAmB,GAAG,MAAM,CAAC;AAG/B,UAAI,iBAAiB;AACnB,YAAI,uBAAuB,gBAAgB,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,MAAM,GAAG;AAG7E,qBAAW,KACT,kCACE,aACA,wBACA,iBACA,wBAAwB,CACzB;QAEL;MACF;AACA,UAAI,oBAAoB,MAAM;AAC5B,mBAAW,KAAK,kCAAkC,aAAa,mBAAmB,CAAC;MACrF;AAEA,YAAM,UAAU,GAAG,aACjB,GAAG,QAAQ,gBAAgB,YAAY,UAAU,QAAQ,gBAAgB,GACzE,UAAU,OAAO;AAGnB,aAAO,GAAG,QAAQ,uBAChB,WACA,UAAU,WACV,UAAU,MACV,UAAU,gBACV,UAAU,iBACV,OAAO;IAEX;AAOA,aAAS,0BAA0B,MAAa;AAC9C,UACE,GAAG,mBAAmB,IAAI,MACzB,yBAAyB,UAAa,qBAAqB,IAAI,IAChE;AACA,eAAO,0BAA0B,IAAI;MACvC;AACA,aAAO,GAAG,eAAe,MAAM,2BAA2B,OAAO;IACnE;AAEA,WAAO,CAAC,OAAqB;AAI3B,aAAO,GAAG,eAAe,IAAI,2BAA2B,OAAO;IACjE;EACF;AACF;AAEA,SAAS,+BACP,MACA,WAAiD;AAEjD,MAAI;AAEJ,MAAI,GAAG,oBAAoB,IAAI,GAAG;AAChC,YAAQ,GAAG,QAAQ,wBACjB,WACA,KAAK,eACL,KAAK,MACL,KAAK,eACL,KAAK,gBACL,KAAK,YACL,KAAK,MACL,KAAK,IAAI;EAEb,WAAW,GAAG,sBAAsB,IAAI,GAAG;AACzC,YAAQ,GAAG,QAAQ,0BACjB,WACA,KAAK,MACL,KAAK,eACL,KAAK,MACL,KAAK,WAAW;EAEpB,WAAW,GAAG,cAAc,IAAI,GAAG;AACjC,YAAQ,GAAG,QAAQ,6BACjB,WACA,KAAK,MACL,KAAK,YACL,KAAK,MACL,KAAK,IAAI;EAEb,WAAW,GAAG,cAAc,IAAI,GAAG;AACjC,YAAQ,GAAG,QAAQ,6BACjB,WACA,KAAK,MACL,KAAK,YACL,KAAK,IAAI;EAEb,OAAO;AACL,UAAM,IAAI,MAAM,0CAA0C,GAAG,WAAW,KAAK,OAAO;EACtF;AAEA,SAAO,GAAG,gBAAgB,OAAO,IAAI;AACvC;;;ACppBA,OAAOE,SAAQ;;;ACAf,OAAOC,SAAQ;AAwBT,SAAU,0CACd,SACA,eACA,kBACA,YACA,eAAqB;AAErB,QAAM,2BAA2BA,IAAG,aAAa,iBAAiB,UAAU,IACxE,iBAAiB,aACjB,iBAAiB,WAAW;AAEhC,SAAO,QAAQ;IACb,cAAc,UAAU;MACtB,uBAAuB;MACvB,kBAAkB;MAClB,eAAe;KAChB;IAIDA,IAAG,gBAAgB,QAAQ,iBAAiB,aAAa,GAAG,wBAAwB;EAAC;AAEzF;AAGM,SAAU,UAAU,SAAyB,MAAmB;AACpE,SAAO,QAAQ,mBAAmB,MAAM,QAAQ,sBAAsBA,IAAG,WAAW,UAAU,CAAC;AACjG;;;AC7BO,IAAM,wBAA2C,CACtD,QACA,YACA,MACA,SACA,eACA,eACA,gBACA,WACE;AAvCJ;AAyCE,OACE,UACG,2BAA2B,OAAO,IAAI,MADzC,mBAEI,KAAK,CAAC,MAAM,mBAAmB,GAAG,SAAS,MAAM,IACrD;AACA,WAAO,OAAO;EAChB;AAEA,QAAM,eAAe,2BAA2B,QAAQ,MAAM,aAAa;AAC3E,MAAI,iBAAiB,MAAM;AACzB,WAAO,OAAO;EAChB;AAEA,QAAM,SAA4D;IAChE,YAAY,QAAQ,WAAU;IAC9B,SAAS,QAAQ,oBAAoB,aAAa,mBAAmB;IACrE,YAAY,aAAa,WAAW,QAAQ,WAAU,IAAK,QAAQ,YAAW;IAI9E,aAAa,QAAQ,iBAAiB,WAAW;;AAGnD,QAAM,eAAe,QAAQ,gBAC3B,QAAQ,qBACN,0CACE,SACA,eACA,gBACA,YACA,OAAO,GAET,QACA;IAIE,UACE,SACA,QAAQ,8BACN,OAAO,QAAQ,MAAM,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MACtC,QAAQ,yBAAyB,MAAM,KAAK,CAAC,CAC9C,CACF;GAEJ,CACF;AAGH,SAAO,QAAQ,0BACb,OAAO,MACP,CAAC,cAAc,IAAI,YAAO,KAAK,cAAZ,YAAyB,CAAA,CAAG,GAC/C,OAAO,MACP,OAAO,KAAK,eACZ,OAAO,KAAK,MACZ,OAAO,KAAK,WAAW;AAE3B;;;ACzFA,OAAOC,SAAQ;AAWR,IAAM,uBAA0C,CACrD,QACA,YACA,MACA,SACA,eACA,eACA,gBACA,WACE;AA7BJ;AA8BE,OACE,UAAK,2BAA2B,OAAO,IAAI,MAA3C,mBAA8C,KAAK,CAAC,MAAK;AACvD,WAAO,mBAAmB,GAAG,SAAS,MAAM,KAAK,mBAAmB,GAAG,UAAU,MAAM;EACzF,IACA;AACA,WAAO,OAAO;EAChB;AAEA,QAAM,eAAe,2BAA2B,QAAQ,MAAM,aAAa;AAE3E,MAAI,iBAAiB,MAAM;AACzB,WAAO,OAAO;EAChB;AAEA,QAAM,cAAc,QAAQ,8BAA8B;IACxD,QAAQ,yBACN,YACA,aAAa,MAAM,WAAW,QAAQ,WAAU,IAAK,QAAQ,YAAW,CAAE;IAE5E,QAAQ,yBACN,SACA,QAAQ,oBAAoB,aAAa,MAAM,mBAAmB,CAAC;IAErE,QAAQ,yBACN,YACA,aAAa,MAAM,WAAW,QAAQ,WAAU,IAAK,QAAQ,YAAW,CAAE;GAE7E;AAED,QAAM,iBAAiB;IACrB;IAIA,QAAQ,mBACN,aACA,QAAQ,sBAAsBC,IAAG,WAAW,UAAU,CAAC;IAEzD;IACA;IACA;IACA;EAAa;AAGf,QAAM,kBAAkB,gBACtB,UACA,QAAQ,oBAAoB,aAAa,OAAO,mBAAmB,GACnE,gBACA,SACA,YACA,aAAa;AAGf,SAAO,QAAQ,0BACb,OAAO,MACP,CAAC,gBAAgB,iBAAiB,IAAI,YAAO,KAAK,cAAZ,YAAyB,CAAA,CAAG,GAClE,OAAO,KAAK,MACZ,OAAO,KAAK,eACZ,OAAO,KAAK,MACZ,OAAO,KAAK,WAAW;AAE3B;AAEA,SAAS,gBACP,MACA,QACA,gBACA,SACA,YACA,eAA4B;AAE5B,QAAM,aAAa,0CACjB,SACA,eACA,gBACA,YACA,IAAI;AAGN,SAAO,QAAQ,gBAAgB,QAAQ,qBAAqB,YAAY,QAAW,CAAC,MAAM,CAAC,CAAC;AAC9F;;;ACxFO,IAAM,gCAAmD,CAC9D,QACA,YACA,MACA,SACA,eACA,eACA,gBACA,WACE;AA/BJ;AAiCE,OACE,UACG,2BAA2B,OAAO,IAAI,MADzC,mBAEI,KAAK,CAAC,MAAM,mBAAmB,GAAG,UAAU,MAAM,IACtD;AACA,WAAO,OAAO;EAChB;AAEA,QAAM,SAAS,+BAA+B,QAAQ,MAAM,aAAa;AACzE,MAAI,WAAW,MAAM;AACnB,WAAO,OAAO;EAChB;AAEA,QAAM,eAAe,QAAQ,gBAC3B,QAAQ,qBACN,0CACE,SACA,eACA,gBACA,YACA,QAAQ,GAEV,QACA,CAAC,QAAQ,oBAAoB,OAAO,SAAS,mBAAmB,CAAC,CAAC,CACnE;AAGH,SAAO,QAAQ,0BACb,OAAO,MACP,CAAC,cAAc,IAAI,YAAO,KAAK,cAAZ,YAAyB,CAAA,CAAG,GAC/C,OAAO,KAAK,MACZ,OAAO,KAAK,eACZ,OAAO,KAAK,MACZ,OAAO,KAAK,WAAW;AAE3B;;;AC9CA,IAAM,2BAA8D;EAClE,WAAW;EACX,cAAc;EACd,cAAc;EACd,iBAAiB;;AAcZ,IAAM,2BAA8C,CACzD,QACA,YACA,MACA,SACA,eACA,eACA,gBACA,WACE;AAjDJ;AAkDE,QAAM,aAAa,KAAK,2BAA2B,OAAO,IAAI;AAG9D,QAAM,kBACJ,cAAc,qBAAqB,YAAY,qBAAqB,MAAM;AAC5E,MAAI,oBAAoB,QAAQ,gBAAgB,SAAS,GAAG;AAC1D,WAAO,OAAO;EAChB;AAEA,QAAM,kBAAkB,mCAAmC,QAAQ,MAAM,aAAa;AACtF,MAAI,oBAAoB,MAAM;AAC5B,WAAO,OAAO;EAChB;AAEA,QAAM,WAAW,gBAAgB,KAAK;AACtC,QAAM,eAAe,QAAQ,gBAC3B,QAAQ;IACN,0CACE,SACA,eACA,gBACA,YACA,yBAAyB,gBAAgB,KAAK;IAEhD;IAGA;MACE,gBAAgB,KAAK,UAAU;MAG/B,UACE,SACA,QAAQ,8BAA8B;QACpC,GAAI,SAAS,SAAS,IAAI,CAAC,QAAQ,uBAAuB,SAAS,EAAE,CAAC,IAAI,CAAA;QAC1E,QAAQ,yBAAyB,YAAY,QAAQ,WAAU,CAAE;OAClE,CAAC;;EAEL,CACF;AAGH,SAAO,QAAQ,0BACb,OAAO,MACP,CAAC,cAAc,IAAI,YAAO,KAAK,cAAZ,YAAyB,CAAA,CAAG,GAC/C,OAAO,KAAK,MACZ,OAAO,KAAK,eACZ,OAAO,KAAK,MACZ,OAAO,KAAK,WAAW;AAE3B;;;AL7EA,IAAM,uBAAuB,CAAC,aAAa,WAAW;AAMtD,IAAM,qBAA0C;EAC9C;EACA;EACA;EACA;;AAeI,SAAU,8BACd,MACA,eACA,QACA,sBAA6D;AAE7D,SAAO,CAAC,QAAO;AACb,WAAO,CAAC,eAAc;AACpB,YAAM,gBAAgB,IAAI,cAAa;AAEvC,mBAAaC,IAAG,UACd,YACA,uBACE,KACA,MACA,eACA,eACA,QACA,oBAAoB,GAEtBA,IAAG,YAAY;AAGjB,aAAO,cAAc,gBAAgB,KAAK,UAAU;IACtD;EACF;AACF;AAEA,SAAS,uBACP,KACA,MACA,eACA,eACA,QACA,sBAA6D;AAE7D,QAAM,UAAwC,CAAC,SAA0B;AApF3E;AAqFI,QAAIA,IAAG,mBAAmB,IAAI,KAAK,KAAK,SAAS,QAAW;AAC1D,YAAM,eAAeA,IAAG,gBAAgB,MAAMA,IAAG,kBAAkB;AAInE,YAAM,oBAAmB,UACtB,2BAA2B,YAAY,MADjB,mBAErB,KAAK,CAAC,MAAM,qBAAqB,KAAK,CAAC,SAAS,mBAAmB,GAAG,MAAM,MAAM,CAAC;AAEvF,UACE,qBAAqB,WACpB,yBAAyB,UAAa,qBAAqB,IAAI,IAChE;AACA,YAAI,aAAa;AAEjB,cAAM,aAAa,aAAa,cAAa;AAC7C,cAAM,UAAU,KAAK,QAAQ,IAAI,CAAC,eAAc;AAC9C,cAAI,CAACA,IAAG,sBAAsB,UAAU,GAAG;AACzC,mBAAO;UACT;AACA,gBAAM,SAAS,mBAAmB,UAAU;AAC5C,cAAI,WAAW,MAAM;AACnB,mBAAO;UACT;AAGA,qBAAW,aAAa,oBAAoB;AAC1C,kBAAM,UAAU,UACd,EAAC,GAAG,QAAQ,MAAM,WAAU,GAC5B,YACA,MACA,IAAI,SACJ,eACA,eACA,kBACA,MAAM;AAGR,gBAAI,YAAY,OAAO,MAAM;AAC3B,2BAAa;AACb,qBAAO;YACT;UACF;AAEA,iBAAO;QACT,CAAC;AAED,YAAI,YAAY;AACd,iBAAO,IAAI,QAAQ,uBACjB,MACA,KAAK,WACL,KAAK,MACL,KAAK,gBACL,KAAK,iBACL,OAAO;QAEX;MACF;IACF;AAEA,WAAOA,IAAG,eAAe,MAAM,SAAS,GAAG;EAC7C;AACA,SAAO;AACT;;;AM/GM,SAAU,+BACd,SACA,SAAS,OACT,sBAA6D;AAE7D,QAAM,cAAc,QAAQ,eAAc;AAC1C,QAAM,iBAAiB,IAAI,yBAAyB,WAAW;AAC/D,QAAM,gBAAgB,IAAI,uBAAsB;AAEhD,QAAM,8BAA8B;IAClC;IACA;IACA,CAAA;IACA;IAC4B;IAC5B;EAAoB;AAGtB,QAAM,8BAA8B,8BAClC,gBACA,eACA,QACA,oBAAoB;AAGtB,SAAO,CAAC,QAAO;AACb,WAAO,CAAC,eAAc;AACpB,mBAAa,4BAA4B,GAAG,EAAE,UAAU;AACxD,mBAAa,4BAA4B,GAAG,EAAE,UAAU;AAExD,aAAO;IACT;EACF;AACF;", "names": ["isAngularDecorator", "diagnostics", "ts", "ts", "ts", "ts", "ts"]}