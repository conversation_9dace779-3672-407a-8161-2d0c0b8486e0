/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import type { LoaderContext } from 'webpack';
export declare function angularWebpackLoader(this: LoaderContext<unknown>, content: string, map: string): void;
export { angularWebpackLoader as default };
