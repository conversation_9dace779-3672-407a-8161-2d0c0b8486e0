{"name": "jasmine-core", "license": "MIT", "version": "5.4.0", "repository": {"type": "git", "url": "https://github.com/jasmine/jasmine.git"}, "keywords": ["test", "testing", "jasmine", "tdd", "bdd"], "scripts": {"posttest": "eslint \"src/**/*.js\" \"spec/**/*.js\" && prettier --check \"src/**/*.js\" \"spec/**/*.js\"", "test": "grunt --stack execSpecsInNode", "cleanup": "prettier --write \"src/**/*.js\" \"spec/**/*.js\"", "build": "grunt buildDistribution", "serve": "node spec/support/localJasmineBrowser.js", "serve:performance": "node spec/support/localJasmineBrowser.js jasmine-browser-performance.json", "ci": "node spec/support/ci.js", "ci:performance": "node spec/support/ci.js jasmine-browser-performance.json"}, "description": "Simple JavaScript testing framework for browsers and node.js", "homepage": "https://jasmine.github.io", "main": "./lib/jasmine-core.js", "files": ["LICENSE", "README.md", "images/*.{png,svg}", "lib/**/*.{js,css}", "package.json"], "devDependencies": {"eslint": "^8.36.0", "eslint-plugin-compat": "^4.0.0", "glob": "^10.2.3", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-compress": "^2.0.0", "grunt-contrib-concat": "^2.0.0", "grunt-css-url-embed": "^1.11.1", "grunt-sass": "^3.0.2", "jasmine": "^5.0.0", "jasmine-browser-runner": "github:jasmine/jasmine-browser-runner", "jsdom": "^22.0.0", "load-grunt-tasks": "^5.1.0", "prettier": "1.17.1", "sass": "^1.58.3", "shelljs": "^0.8.3", "temp": "^0.9.0"}, "browserslist": ["Safari >= 15", "Firefox >= 102", "last 2 Chrome versions", "last 2 Edge versions"]}