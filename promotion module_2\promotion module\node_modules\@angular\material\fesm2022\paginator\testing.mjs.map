{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/paginator/testing/paginator-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {MatSelectHarness} from '../../select/testing';\nimport {coerceNumberProperty} from '@angular/cdk/coercion';\nimport {PaginatorHarnessFilters} from './paginator-harness-filters';\n\n/** Harness for interacting with a mat-paginator in tests. */\nexport class MatPaginatorHarness extends ComponentHarness {\n  /** Selector used to find paginator instances. */\n  static hostSelector = '.mat-mdc-paginator';\n  private _nextButton = this.locatorFor('.mat-mdc-paginator-navigation-next');\n  private _previousButton = this.locatorFor('.mat-mdc-paginator-navigation-previous');\n  private _firstPageButton = this.locatorForOptional('.mat-mdc-paginator-navigation-first');\n  private _lastPageButton = this.locatorForOptional('.mat-mdc-paginator-navigation-last');\n  _select = this.locatorForOptional(\n    MatSelectHarness.with({\n      ancestor: '.mat-mdc-paginator-page-size',\n    }),\n  );\n  private _pageSizeFallback = this.locatorFor('.mat-mdc-paginator-page-size-value');\n  _rangeLabel = this.locatorFor('.mat-mdc-paginator-range-label');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a paginator with specific attributes.\n   * @param options Options for filtering which paginator instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatPaginatorHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: PaginatorHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Goes to the next page in the paginator. */\n  async goToNextPage(): Promise<void> {\n    return (await this._nextButton()).click();\n  }\n\n  /** Returns whether or not the next page button is disabled. */\n  async isNextPageDisabled(): Promise<boolean> {\n    const disabledValue = await (await this._nextButton()).getAttribute('aria-disabled');\n    return disabledValue == 'true';\n  }\n\n  /* Returns whether or not the previous page button is disabled. */\n  async isPreviousPageDisabled(): Promise<boolean> {\n    const disabledValue = await (await this._previousButton()).getAttribute('aria-disabled');\n    return disabledValue == 'true';\n  }\n\n  /** Goes to the previous page in the paginator. */\n  async goToPreviousPage(): Promise<void> {\n    return (await this._previousButton()).click();\n  }\n\n  /** Goes to the first page in the paginator. */\n  async goToFirstPage(): Promise<void> {\n    const button = await this._firstPageButton();\n\n    // The first page button isn't enabled by default so we need to check for it.\n    if (!button) {\n      throw Error(\n        'Could not find first page button inside paginator. ' +\n          'Make sure that `showFirstLastButtons` is enabled.',\n      );\n    }\n\n    return button.click();\n  }\n\n  /** Goes to the last page in the paginator. */\n  async goToLastPage(): Promise<void> {\n    const button = await this._lastPageButton();\n\n    // The last page button isn't enabled by default so we need to check for it.\n    if (!button) {\n      throw Error(\n        'Could not find last page button inside paginator. ' +\n          'Make sure that `showFirstLastButtons` is enabled.',\n      );\n    }\n\n    return button.click();\n  }\n\n  /**\n   * Sets the page size of the paginator.\n   * @param size Page size that should be select.\n   */\n  async setPageSize(size: number): Promise<void> {\n    const select = await this._select();\n\n    // The select is only available if the `pageSizeOptions` are\n    // set to an array with more than one item.\n    if (!select) {\n      throw Error(\n        'Cannot find page size selector in paginator. ' +\n          'Make sure that the `pageSizeOptions` have been configured.',\n      );\n    }\n\n    return select.clickOptions({text: `${size}`});\n  }\n\n  /** Gets the page size of the paginator. */\n  async getPageSize(): Promise<number> {\n    const select = await this._select();\n    const value = select ? select.getValueText() : (await this._pageSizeFallback()).text();\n    return coerceNumberProperty(await value);\n  }\n\n  /** Gets the text of the range label of the paginator. */\n  async getRangeLabel(): Promise<string> {\n    return (await this._rangeLabel()).text();\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAiBA;AACM,MAAO,mBAAoB,SAAQ,gBAAgB,CAAA;;AAEvD,IAAA,OAAO,YAAY,GAAG,oBAAoB;AAClC,IAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,oCAAoC,CAAC;AACnE,IAAA,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,wCAAwC,CAAC;AAC3E,IAAA,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,qCAAqC,CAAC;AACjF,IAAA,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,oCAAoC,CAAC;IACvF,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAC/B,gBAAgB,CAAC,IAAI,CAAC;AACpB,QAAA,QAAQ,EAAE,8BAA8B;AACzC,KAAA,CAAC,CACH;AACO,IAAA,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,oCAAoC,CAAC;AACjF,IAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,gCAAgC,CAAC;AAE/D;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAmC,EAAE,EAAA;AAErC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;;;AAI5C,IAAA,MAAM,YAAY,GAAA;QAChB,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE;;;AAI3C,IAAA,MAAM,kBAAkB,GAAA;AACtB,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC;QACpF,OAAO,aAAa,IAAI,MAAM;;;AAIhC,IAAA,MAAM,sBAAsB,GAAA;AAC1B,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,eAAe,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC;QACxF,OAAO,aAAa,IAAI,MAAM;;;AAIhC,IAAA,MAAM,gBAAgB,GAAA;QACpB,OAAO,CAAC,MAAM,IAAI,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE;;;AAI/C,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE;;QAG5C,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,KAAK,CACT,qDAAqD;AACnD,gBAAA,mDAAmD,CACtD;;AAGH,QAAA,OAAO,MAAM,CAAC,KAAK,EAAE;;;AAIvB,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE;;QAG3C,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,KAAK,CACT,oDAAoD;AAClD,gBAAA,mDAAmD,CACtD;;AAGH,QAAA,OAAO,MAAM,CAAC,KAAK,EAAE;;AAGvB;;;AAGG;IACH,MAAM,WAAW,CAAC,IAAY,EAAA;AAC5B,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;;;QAInC,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,KAAK,CACT,+CAA+C;AAC7C,gBAAA,4DAA4D,CAC/D;;AAGH,QAAA,OAAO,MAAM,CAAC,YAAY,CAAC,EAAC,IAAI,EAAE,CAAA,EAAG,IAAI,CAAA,CAAE,EAAC,CAAC;;;AAI/C,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;QACnC,MAAM,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE;AACtF,QAAA,OAAO,oBAAoB,CAAC,MAAM,KAAK,CAAC;;;AAI1C,IAAA,MAAM,aAAa,GAAA;QACjB,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE;;;;;;"}