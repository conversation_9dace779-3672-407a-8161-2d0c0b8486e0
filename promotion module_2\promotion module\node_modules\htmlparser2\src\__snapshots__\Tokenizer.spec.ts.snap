// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Tokenizer > should correctly mark attributes > for double quotes attribute 1`] = `
[
  [
    "onopentagname",
    1,
    4,
  ],
  [
    "onattribname",
    5,
    8,
  ],
  [
    "onattribdata",
    10,
    11,
  ],
  [
    "onattribend",
    3,
    12,
  ],
  [
    "onopentagend",
    13,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should correctly mark attributes > for no quotes attribute 1`] = `
[
  [
    "onopentagname",
    1,
    4,
  ],
  [
    "onattribname",
    5,
    8,
  ],
  [
    "onattribdata",
    9,
    12,
  ],
  [
    "onattribend",
    1,
    12,
  ],
  [
    "onopentagend",
    13,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should correctly mark attributes > for no value attribute 1`] = `
[
  [
    "onopentagname",
    1,
    4,
  ],
  [
    "onattribname",
    5,
    12,
  ],
  [
    "onattribend",
    0,
    12,
  ],
  [
    "onopentagend",
    13,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should correctly mark attributes > for single quotes attribute 1`] = `
[
  [
    "onopentagname",
    1,
    4,
  ],
  [
    "onattribname",
    5,
    8,
  ],
  [
    "onattribdata",
    10,
    11,
  ],
  [
    "onattribend",
    2,
    12,
  ],
  [
    "onopentagend",
    13,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should handle entities > for XML entities 1`] = `
[
  [
    "ontextentity",
    38,
    5,
  ],
  [
    "ontextentity",
    62,
    9,
  ],
  [
    "ontext",
    9,
    13,
  ],
  [
    "ontextentity",
    60,
    17,
  ],
  [
    "ontext",
    17,
    23,
  ],
  [
    "ontextentity",
    97,
    29,
  ],
  [
    "ontext",
    29,
    34,
  ],
  [
    "ontextentity",
    99,
    39,
  ],
  [
    "ontext",
    39,
    49,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should handle entities > for entities in attributes (#276) 1`] = `
[
  [
    "onopentagname",
    1,
    4,
  ],
  [
    "onattribname",
    5,
    8,
  ],
  [
    "onattribdata",
    10,
    24,
  ],
  [
    "onattribentity",
    8465,
  ],
  [
    "onattribdata",
    31,
    41,
  ],
  [
    "onattribend",
    3,
    42,
  ],
  [
    "onselfclosingtag",
    43,
  ],
  [
    "ontext",
    44,
    58,
  ],
  [
    "ontextentity",
    8465,
    65,
  ],
  [
    "ontext",
    65,
    75,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should handle entities > for multi-byte entities 1`] = `
[
  [
    "ontextentity",
    8807,
    21,
  ],
  [
    "ontextentity",
    824,
    21,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should handle entities > for trailing legacy entity 1`] = `
[
  [
    "ontextentity",
    10801,
    10,
  ],
  [
    "ontextentity",
    215,
    16,
  ],
  [
    "ontext",
    16,
    19,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should not break after special tag followed by an entity > for normal special tag 1`] = `
[
  [
    "onopentagname",
    1,
    6,
  ],
  [
    "onopentagend",
    6,
  ],
  [
    "ontext",
    7,
    10,
  ],
  [
    "onclosetag",
    12,
    17,
  ],
  [
    "ontextentity",
    39,
    24,
  ],
  [
    "onopentagname",
    25,
    27,
  ],
  [
    "onselfclosingtag",
    28,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should not break after special tag followed by an entity > for self-closing special tag 1`] = `
[
  [
    "onopentagname",
    1,
    6,
  ],
  [
    "onselfclosingtag",
    8,
  ],
  [
    "ontextentity",
    39,
    15,
  ],
  [
    "onopentagname",
    16,
    18,
  ],
  [
    "onselfclosingtag",
    19,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should not lose data when pausing 1`] = `
[
  [
    "ontextentity",
    38,
    5,
  ],
  [
    "ontext",
    5,
    12,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support self-closing special tags > for self-closing script tag 1`] = `
[
  [
    "onopentagname",
    1,
    7,
  ],
  [
    "onselfclosingtag",
    9,
  ],
  [
    "onopentagname",
    11,
    14,
  ],
  [
    "onopentagend",
    14,
  ],
  [
    "onclosetag",
    17,
    20,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support self-closing special tags > for self-closing style tag 1`] = `
[
  [
    "onopentagname",
    1,
    6,
  ],
  [
    "onselfclosingtag",
    8,
  ],
  [
    "onopentagname",
    10,
    13,
  ],
  [
    "onopentagend",
    13,
  ],
  [
    "onclosetag",
    16,
    19,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support self-closing special tags > for self-closing textarea tag 1`] = `
[
  [
    "onopentagname",
    1,
    9,
  ],
  [
    "onselfclosingtag",
    11,
  ],
  [
    "onopentagname",
    13,
    16,
  ],
  [
    "onopentagend",
    16,
  ],
  [
    "onclosetag",
    19,
    22,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support self-closing special tags > for self-closing title tag 1`] = `
[
  [
    "onopentagname",
    1,
    6,
  ],
  [
    "onselfclosingtag",
    8,
  ],
  [
    "onopentagname",
    10,
    13,
  ],
  [
    "onopentagend",
    13,
  ],
  [
    "onclosetag",
    16,
    19,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support self-closing special tags > for self-closing xmp tag 1`] = `
[
  [
    "onopentagname",
    1,
    4,
  ],
  [
    "onselfclosingtag",
    6,
  ],
  [
    "onopentagname",
    8,
    11,
  ],
  [
    "onopentagend",
    11,
  ],
  [
    "onclosetag",
    14,
    17,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support standard special tags > for normal script tag 1`] = `
[
  [
    "onopentagname",
    1,
    7,
  ],
  [
    "onopentagend",
    7,
  ],
  [
    "onclosetag",
    10,
    16,
  ],
  [
    "onopentagname",
    18,
    21,
  ],
  [
    "onopentagend",
    21,
  ],
  [
    "onclosetag",
    24,
    27,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support standard special tags > for normal sitle tag 1`] = `
[
  [
    "onopentagname",
    1,
    6,
  ],
  [
    "onopentagend",
    6,
  ],
  [
    "onclosetag",
    9,
    14,
  ],
  [
    "onopentagname",
    16,
    19,
  ],
  [
    "onopentagend",
    19,
  ],
  [
    "onclosetag",
    22,
    25,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support standard special tags > for normal style tag 1`] = `
[
  [
    "onopentagname",
    1,
    6,
  ],
  [
    "onopentagend",
    6,
  ],
  [
    "onclosetag",
    9,
    14,
  ],
  [
    "onopentagname",
    16,
    19,
  ],
  [
    "onopentagend",
    19,
  ],
  [
    "onclosetag",
    22,
    25,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support standard special tags > for normal textarea tag 1`] = `
[
  [
    "onopentagname",
    1,
    9,
  ],
  [
    "onopentagend",
    9,
  ],
  [
    "onclosetag",
    12,
    20,
  ],
  [
    "onopentagname",
    22,
    25,
  ],
  [
    "onopentagend",
    25,
  ],
  [
    "onclosetag",
    28,
    31,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should support standard special tags > for normal xmp tag 1`] = `
[
  [
    "onopentagname",
    1,
    4,
  ],
  [
    "onopentagend",
    4,
  ],
  [
    "onclosetag",
    7,
    10,
  ],
  [
    "onopentagname",
    12,
    15,
  ],
  [
    "onopentagend",
    15,
  ],
  [
    "onclosetag",
    18,
    21,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should treat html inside special tags as text > for div inside script tag 1`] = `
[
  [
    "onopentagname",
    1,
    7,
  ],
  [
    "onopentagend",
    7,
  ],
  [
    "ontext",
    8,
    19,
  ],
  [
    "onclosetag",
    21,
    27,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should treat html inside special tags as text > for div inside style tag 1`] = `
[
  [
    "onopentagname",
    1,
    6,
  ],
  [
    "onopentagend",
    6,
  ],
  [
    "ontext",
    7,
    18,
  ],
  [
    "onclosetag",
    20,
    25,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should treat html inside special tags as text > for div inside textarea tag 1`] = `
[
  [
    "onopentagname",
    1,
    9,
  ],
  [
    "onopentagend",
    9,
  ],
  [
    "ontext",
    10,
    21,
  ],
  [
    "onclosetag",
    23,
    31,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should treat html inside special tags as text > for div inside title tag 1`] = `
[
  [
    "onopentagname",
    1,
    6,
  ],
  [
    "onopentagend",
    6,
  ],
  [
    "ontext",
    7,
    18,
  ],
  [
    "onclosetag",
    20,
    25,
  ],
  [
    "onend",
  ],
]
`;

exports[`Tokenizer > should treat html inside special tags as text > for div inside xmp tag 1`] = `
[
  [
    "onopentagname",
    1,
    4,
  ],
  [
    "onopentagend",
    4,
  ],
  [
    "ontext",
    5,
    16,
  ],
  [
    "onclosetag",
    18,
    21,
  ],
  [
    "onend",
  ],
]
`;
