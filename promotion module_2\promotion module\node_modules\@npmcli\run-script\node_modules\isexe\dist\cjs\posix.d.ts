/**
 * This is the Posix implementation of isexe, which uses the file
 * mode and uid/gid values.
 *
 * @module
 */
import { IsexeOptions } from './options';
/**
 * Determine whether a path is executable according to the mode and
 * current (or specified) user and group IDs.
 */
export declare const isexe: (path: string, options?: IsexeOptions) => Promise<boolean>;
/**
 * Synchronously determine whether a path is executable according to
 * the mode and current (or specified) user and group IDs.
 */
export declare const sync: (path: string, options?: IsexeOptions) => boolean;
//# sourceMappingURL=posix.d.ts.map