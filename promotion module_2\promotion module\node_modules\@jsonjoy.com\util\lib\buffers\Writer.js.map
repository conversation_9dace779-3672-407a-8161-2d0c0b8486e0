{"version": 3, "file": "Writer.js", "sourceRoot": "", "sources": ["../../src/buffers/Writer.ts"], "names": [], "mappings": ";;;AAAA,mCAA8B;AAG9B,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AACvC,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAEpD,MAAM,SAAS,GAAG,OAAO,MAAM,KAAK,UAAU,CAAC;AAC/C,MAAM,SAAS,GAAG,SAAS;IACzB,CAAC,CAAE,MAAM,CAAC,SAAS,CAAC,SAAuF;IAC3G,CAAC,CAAC,IAAI,CAAC;AACT,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC5C,MAAM,WAAW,GAAG,OAAO,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;AAOlF,MAAa,MAAM;IAcjB,YAAmB,YAAoB,EAAE,GAAG,IAAI;QAA7B,cAAS,GAAT,SAAS,CAAoB;QAVzC,SAAI,GAAa,UAAU,CAAC;QAE5B,OAAE,GAAW,CAAC,CAAC;QAEf,MAAC,GAAW,CAAC,CAAC;QAOnB,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAGS,IAAI,CAAC,IAAY;QACzB,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;QACtC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3C,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC7C,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAQM,cAAc,CAAC,QAAgB;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,MAAM,SAAS,GAAG,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC;QACtC,IAAI,SAAS,GAAG,QAAQ,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;YACtC,MAAM,aAAa,GAAG,KAAK,GAAG,QAAQ,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAGM,IAAI,CAAC,QAAgB;QAC1B,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC9B,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC;IACrB,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IACnB,CAAC;IAQM,SAAS,CAAC,IAAY;QAC3B,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACvB,CAAC;IAKM,KAAK;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QACjB,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,UAAU;QACf,MAAM,KAAK,GAAG,IAAI,aAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAChE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QACjB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,EAAE,CAAC,IAAY;QACpB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;IAC9B,CAAC;IAEM,GAAG,CAAC,IAAY;QACrB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACd,CAAC;IAEM,GAAG,CAAC,KAAa;QACtB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACd,CAAC;IAEM,GAAG,CAAC,KAAa;QACtB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACd,CAAC;IAEM,GAAG,CAAC,KAAsB;QAC/B,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACd,CAAC;IAEM,GAAG,CAAC,KAAa;QACtB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,EAAU,EAAE,GAAW;QAClC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACb,CAAC;IAEM,KAAK,CAAC,EAAU,EAAE,GAAW;QAClC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,EAAU,EAAE,GAAoB;QAC3C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,EAAU,EAAE,GAAW;QAClC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,EAAU,EAAE,GAAW;QAClC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAEM,GAAG,CAAC,GAAe,EAAE,MAAc;QACxC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;IACtB,CAAC;IASM,IAAI,CAAC,GAAW;QACrB,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QACjC,IAAI,SAAS,GAAG,GAAG;YAAE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YACvE,IAAI,CAAC,CAAC,IAAI,WAAW,CAAC;YACtB,OAAO,WAAW,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,EAAE,CAAC;YAChB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC;YACzC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YACzD,IAAI,CAAC,CAAC,IAAI,WAAW,CAAC;YACtB,OAAO,WAAW,CAAC;QACrB,CAAC;aAAM,IAAI,SAAS,GAAG,IAAI,IAAI,WAAW,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAG,WAAY,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,OAAQ,CAAC;YAC3G,IAAI,CAAC,CAAC,IAAI,WAAW,CAAC;YACtB,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAEM,UAAU,CAAC,GAAW;QAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;QACpB,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,OAAO,GAAG,GAAG,MAAM,EAAE,CAAC;YACpB,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;YAClC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/B,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC;gBACxB,SAAS;YACX,CAAC;iBAAM,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;oBACvC,IAAI,GAAG,GAAG,MAAM,EAAE,CAAC;wBACjB,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBAClC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE,CAAC;4BAChC,GAAG,EAAE,CAAC;4BACN,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;wBAC9D,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC/B,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;oBAChD,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACjD,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;oBAChD,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;oBAChD,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACjD,CAAC;YACH,CAAC;YACD,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QAC1C,CAAC;QACD,MAAM,WAAW,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;QAChB,OAAO,WAAW,CAAC;IACrB,CAAC;IAEM,KAAK,CAAC,GAAW;QACtB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,OAAO,GAAG,GAAG,MAAM;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACb,CAAC;CACF;AA1PD,wBA0PC"}