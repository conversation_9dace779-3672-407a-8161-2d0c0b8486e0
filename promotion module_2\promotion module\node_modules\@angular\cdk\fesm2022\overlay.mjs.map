{"version": 3, "file": "overlay.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/overlay/fullscreen-overlay-container.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, Injectable, OnDestroy, RendererFactory2} from '@angular/core';\nimport {OverlayContainer} from './overlay-container';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\n@Injectable({providedIn: 'root'})\nexport class FullscreenOverlayContainer extends OverlayContainer implements OnDestroy {\n  private _renderer = inject(RendererFactory2).createRenderer(null, null);\n  private _fullScreenEventName: string | undefined;\n  private _cleanupFullScreenListener: (() => void) | undefined;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n    this._cleanupFullScreenListener?.();\n  }\n\n  protected override _createContainer(): void {\n    const eventName = this._getEventName();\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n\n    if (eventName) {\n      this._cleanupFullScreenListener?.();\n      this._cleanupFullScreenListener = this._renderer.listen('document', eventName, () => {\n        this._adjustParentForFullscreenChange();\n      });\n    }\n  }\n\n  private _adjustParentForFullscreenChange(): void {\n    if (this._containerElement) {\n      const fullscreenElement = this.getFullscreenElement();\n      const parent = fullscreenElement || this._document.body;\n      parent.appendChild(this._containerElement);\n    }\n  }\n\n  private _getEventName(): string | undefined {\n    if (!this._fullScreenEventName) {\n      const _document = this._document as any;\n\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n\n    return this._fullScreenEventName;\n  }\n\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement(): Element {\n    const _document = this._document as any;\n\n    return (\n      _document.fullscreenElement ||\n      _document.webkitFullscreenElement ||\n      _document.mozFullScreenElement ||\n      _document.msFullscreenElement ||\n      null\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAWA;;;;;;AAMG;AAEG,MAAO,0BAA2B,SAAQ,gBAAgB,CAAA;AACtD,IAAA,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;AAC/D,IAAA,oBAAoB;AACpB,IAAA,0BAA0B;AAIlC,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;;IAGA,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AACnB,QAAA,IAAI,CAAC,0BAA0B,IAAI;;IAGlB,gBAAgB,GAAA;AACjC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE;QACtC,KAAK,CAAC,gBAAgB,EAAE;QACxB,IAAI,CAAC,gCAAgC,EAAE;QAEvC,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,0BAA0B,IAAI;AACnC,YAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,MAAK;gBAClF,IAAI,CAAC,gCAAgC,EAAE;AACzC,aAAC,CAAC;;;IAIE,gCAAgC,GAAA;AACtC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACrD,MAAM,MAAM,GAAG,iBAAiB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI;AACvD,YAAA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;;;IAItC,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC9B,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAgB;AAEvC,YAAA,IAAI,SAAS,CAAC,iBAAiB,EAAE;AAC/B,gBAAA,IAAI,CAAC,oBAAoB,GAAG,kBAAkB;;AACzC,iBAAA,IAAI,SAAS,CAAC,uBAAuB,EAAE;AAC5C,gBAAA,IAAI,CAAC,oBAAoB,GAAG,wBAAwB;;AAC/C,iBAAA,IAAI,SAAS,CAAC,oBAAoB,EAAE;AACzC,gBAAA,IAAI,CAAC,oBAAoB,GAAG,qBAAqB;;AAC5C,iBAAA,IAAI,SAAS,CAAC,mBAAmB,EAAE;AACxC,gBAAA,IAAI,CAAC,oBAAoB,GAAG,oBAAoB;;;QAIpD,OAAO,IAAI,CAAC,oBAAoB;;AAGlC;;;AAGG;IACH,oBAAoB,GAAA;AAClB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAgB;QAEvC,QACE,SAAS,CAAC,iBAAiB;AAC3B,YAAA,SAAS,CAAC,uBAAuB;AACjC,YAAA,SAAS,CAAC,oBAAoB;AAC9B,YAAA,SAAS,CAAC,mBAAmB;AAC7B,YAAA,IAAI;;uGAnEG,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAA1B,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,0BAA0B,cADd,MAAM,EAAA,CAAA;;2FAClB,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBADtC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;;;"}