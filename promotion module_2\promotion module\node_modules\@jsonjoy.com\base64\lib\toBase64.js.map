{"version": 3, "file": "toBase64.js", "sourceRoot": "", "sources": ["../src/toBase64.ts"], "names": [], "mappings": ";;;AAAA,2CAAsC;AACtC,qDAAgD;AAEhD,MAAM,WAAW,GAAG,IAAA,+BAAc,GAAE,CAAC;AAExB,QAAA,QAAQ,GAAG,CAAC,qBAAS;IAChC,CAAC,CAAC,CAAC,KAAiB,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC;IACzD,CAAC,CAAC,CAAC,KAAiB,EAAU,EAAE;QAC5B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAI,MAAM,IAAI,EAAE;YAAE,OAAO,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACpD,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC,CAAC"}